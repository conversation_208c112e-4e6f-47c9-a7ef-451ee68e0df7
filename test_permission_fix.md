# 相册权限修复测试

## 修复内容

### 问题描述
在举报/反馈描述输入框中的添加图片按钮选择"相册"后会一直弹出"小西口语向您申请以下权限"-"相册使用权限"的弹框，应该是首先检查是否已经获取权限，没有获取权限才弹这个框。

### 修复方案
1. 在 `PermissionDialogUtil` 中添加了统一的相册权限检查方法 `checkAndRequestPhotoPermission`
2. 该方法会先检查权限状态，只有在权限未授予时才显示权限申请对话框
3. 更新了所有使用相册选择功能的地方，使其使用统一的权限检查方法

### 修改的文件

#### 1. `lib/common/utils/permission_dialog_util.dart`
- 添加了 `checkAndRequestPhotoPermission` 方法
- 添加了 `_showPhotoPermissionDeniedDialog` 方法
- 导入了 `permission_handler` 包

#### 2. `lib/modules/find/provider/tool_photo_ocr_page_provider.dart`
- 修改 `pickImage` 方法，添加 `BuildContext context` 参数
- 在选择相册前先检查权限

#### 3. `lib/modules/find/pages/tool_photo_ocr_page.dart`
- 更新调用 `pickImage` 方法的地方，传入 `context` 参数

#### 4. `lib/modules/my/pages/feedback_page.dart`
- 删除了重复的 `_checkPhotosPermission` 方法
- 更新 `_openGallery` 方法使用统一的权限检查方法

#### 5. `lib/modules/find/pages/tool_writing_page.dart`
- 导入 `permission_dialog_util.dart`
- 在 `_openGallery` 方法中添加权限检查

#### 6. `lib/modules/find/pages/tool_englist_writing_page.dart`
- 导入 `permission_dialog_util.dart`
- 在 `_openGallery` 方法中添加权限检查

#### 7. `lib/modules/scene_study/provider/create_custom_scene_page_provider.dart`
- 导入 `permission_dialog_util.dart`
- 在 `pickImage` 方法中添加权限检查

### 权限检查逻辑

新的权限检查方法 `checkAndRequestPhotoPermission` 的逻辑：

1. **检查当前权限状态**：使用 `Permission.photos.status` 检查权限状态
2. **权限已授予**：如果权限已经授予，直接返回 `true`
3. **权限被永久拒绝**：显示提示对话框，引导用户去设置页面开启权限
4. **权限未授予**：显示权限申请对话框，用户同意后请求权限
5. **处理权限请求结果**：根据权限请求结果返回相应的布尔值

### 测试步骤

1. **首次使用相册功能**：
   - 点击添加图片按钮选择"相册"
   - 应该弹出权限申请对话框
   - 用户同意后，应该能正常选择图片

2. **权限已授予后再次使用**：
   - 再次点击添加图片按钮选择"相册"
   - 应该直接打开相册选择界面，不再弹出权限申请对话框

3. **权限被拒绝的情况**：
   - 如果用户拒绝权限，应该显示相应的提示
   - 如果权限被永久拒绝，应该引导用户去设置页面

### 预期效果

修复后，用户在使用相册功能时：
- 首次使用时会正常申请权限
- 权限授予后，后续使用不会重复弹出权限申请对话框
- 提供了统一的权限处理逻辑，确保所有相册选择功能的行为一致
