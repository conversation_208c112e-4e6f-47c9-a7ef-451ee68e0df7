import 'dart:convert';

import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/permission_dialog_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../pages/my_create_page.dart'; // 用于 JSON 编码和解码

const String CREATE_CUSTOM_SCENE_HISTORY_KEY = 'create_custom_scene_history';
const int MAX_DESCRIPTION_LENGTH = 200;

enum PublicPermission {
  public,
  private,
}

// 扩展 PublicPermission 枚举
extension PublicPermissionExtension on PublicPermission {
  String get description {
    switch (this) {
      case PublicPermission.public:
        return '公开·所有人可对话';
      case PublicPermission.private:
        return '私密·仅自己可对话';
    }
  }
}

// 隐私类型列表
List<PublicPermission> PermissionList = [
  PublicPermission.public,
  PublicPermission.private,
];

class CreateCustomScenePageProvider extends ChangeNotifier {
  // 场景id
  int sceneId = 0;

  // 页面模式, 默认创建, 也可以是编辑
  String pageMode = 'create';

  String target = '';
  String description = '';
  String imageUrl = '';
  String yourRole = '';
  String aiRole = '';
  // 临时的图片文件
  String tempPictureFile = '';
  // 公开权限
  PublicPermission publicPermission = PublicPermission.public;

  // 是否正在保存(创建, 修改请求)
  bool isSaving = false;
  // 是否正在随机生成
  bool isRandomGenerating = false;
  // 是否正在随机生成场景描述
  bool isRandomGeneratingDescription = false;
  // 是否正在随机生成对话目标
  bool isRandomGeneratingTarget = false;
  // 是否正在随机生成你的角色
  bool isRandomGeneratingYourRole = false;
  // 是否正在随机生成AI的角色
  bool isRandomGeneratingAiRole = false;
  // 我的角色输入框, 是否正在聚焦
  bool isMyRoleInputFocus = false;
  // AI角色输入框, 是否正在聚焦
  bool isAiRoleInputFocus = false;

  final ImagePicker picker = ImagePicker();

  loadData(String? sceneCode) async {
    if (sceneCode != null && sceneCode.isNotEmpty) {
      pageMode = 'edit';
      // 加载场景详情
      await loadSceneDetail(sceneCode);
    } else {
      await loadCreateCustomSceneCache();
    }
    notifyListeners();
  }

  // 加载创建自定义场景本地缓存
  Future<void> loadCreateCustomSceneCache() async {
    final prefs = await SharedPreferences.getInstance();
    String jsonString = prefs.getString(CREATE_CUSTOM_SCENE_HISTORY_KEY) ?? '';
    if (jsonString.isNotEmpty) {
      Map<String, dynamic> data = jsonDecode(jsonString);
      target = data['target'];
      description = data['description'];
      imageUrl = data['imageUrl'];
      yourRole = data['yourRole'];
      aiRole = data['aiRole'];
      publicPermission =
          PublicPermission.values.byName(data['publicPermission']);
    }
  }

  // 保存自定义场景的json数据
  Future<void> saveJsonDataCache() async {
    final prefs = await SharedPreferences.getInstance();
    String jsonString = jsonEncode({
      'target': target,
      'description': description,
      'imageUrl': imageUrl,
      'yourRole': yourRole,
      'aiRole': aiRole,
      'publicPermission': publicPermission.name,
    }); // 将 Map 转换为 JSON 字符串
    await prefs.setString(
        CREATE_CUSTOM_SCENE_HISTORY_KEY, jsonString); // 保存 JSON 字符串
  }


  // 删除本地缓存
  Future<void> deleteJsonDataCache() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(CREATE_CUSTOM_SCENE_HISTORY_KEY);
  }

  // notifyListeners
  @override
  void notifyListeners() {
    super.notifyListeners();
  }

  // 从相册选择图片
  Future<void> pickImage(BuildContext context) async {
    // 检查相册权限
    bool hasPermission = await PermissionDialogUtil.checkAndRequestPhotoPermission(context);
    if (!hasPermission) {
      return;
    }

    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    if (image == null) return;
    // 居中显示 toast 上传中
    Fluttertoast.showToast(msg: '上传中...', gravity: ToastGravity.CENTER);
    await uploadImage(image.path);
    notifyListeners();
  }

  /// 删除选中的图片
  deleteImage() {
    imageUrl = '';
    saveJsonDataCache();
    notifyListeners();
  }

  /// 修改聚焦状态
  modifyFocusState(bool isFocus, String role) {
    if (role == '你的角色') {
      isMyRoleInputFocus = isFocus;
    } else {
      isAiRoleInputFocus = isFocus;
    }
    print('修改聚焦状态: $isFocus, $role');
    notifyListeners();
  }

  // 随机生成
  randomGenerate() async {
    LogUtil.d('随机生成');

    if (isRandomGenerating) return;
    isRandomGenerating = true;
    notifyListeners();

    final result = await Api.randomGenerateScene();
    target = result.data?.target ?? '';
    description = result.data?.description ?? '';
    yourRole = result.data?.myRole ?? '';
    aiRole = result.data?.aiRole ?? '';
    imageUrl = '';

    // 保存本地缓存
    saveJsonDataCache();

    isRandomGenerating = false;

    notifyListeners();
  }

  // 随机生成场景描述
  randomGenerateDescription() async {
    LogUtil.d('随机生成场景描述');
    if (aiRole.isEmpty || yourRole.isEmpty) {
      Fluttertoast.showToast(msg: '请先设置角色', gravity: ToastGravity.CENTER);
      return;
    }

    if (isRandomGeneratingDescription) return;
    isRandomGeneratingDescription = true;
    notifyListeners();

    final result = await Api.randomGenerateSceneDesc(aiRole, yourRole, target);
    description = result.data ?? '';

    isRandomGeneratingDescription = false;
    notifyListeners();
  }

  // 随机生成场景目标
  randomGenerateSceneTarget() async {
    LogUtil.d('随机生成场景目标');
    if (aiRole.isEmpty || yourRole.isEmpty || description.isEmpty) {
      Fluttertoast.showToast(msg: '请先设置角色和描述', gravity: ToastGravity.CENTER);
      return;
    }

    if (isRandomGeneratingTarget) return;
    isRandomGeneratingTarget = true;
    notifyListeners();

    final result = await Api.randomGenerateSceneTarget(aiRole, yourRole, description);
    target = result.data ?? '';

    isRandomGeneratingTarget = false;
    notifyListeners();
  }

  // 随机生成你的角色
  randomGenerateMyRole() async {
    LogUtil.d('随机生成你的角色');

    if (isRandomGeneratingYourRole) return;
    isRandomGeneratingYourRole = true;
    notifyListeners();

    final result = await Api.randomGenerateMyRole();
    yourRole = result.data ?? '';

    isRandomGeneratingYourRole = false;
    notifyListeners();
  }

  // 随机生成AI的角色
  randomGenerateAiRole() async {
    LogUtil.d('随机生成AI的角色');
    if (yourRole.isEmpty) {
      Fluttertoast.showToast(msg: '请先设置你的角色', gravity: ToastGravity.CENTER);
      return;
    }

    if (isRandomGeneratingAiRole) return;
    isRandomGeneratingAiRole = true;
    notifyListeners();

    final result = await Api.randomGenerateAiRole(yourRole);
    aiRole = result.data ?? '';

    isRandomGeneratingAiRole = false;
    notifyListeners();
  }

  // 上传图片
  Future<void> uploadImage(String tempPictureFile) async {
    if (tempPictureFile.isEmpty) return;

    // 上传图片获取网络地址
    var uploadResult = await Api.uploadFile(tempPictureFile,
        topicCode: '', scene: 'community_topics');

    imageUrl = uploadResult.data!.fileUrl!;
  }

  // 选择公开权限
  selectPublicPermission(PublicPermission permission) {
    publicPermission = permission;
    saveJsonDataCache();
    notifyListeners();
  }

  // 创建场景
  createScene(BuildContext context) async {
    if (isSaving) return;

    isSaving = true;
    showLoadingDialog(context);
    try {
      if (pageMode == 'create') {
        // 创建场景
      await Api.createCustomScene(
          yourRole,
          aiRole,
          description,
          publicPermission == PublicPermission.private ? 1 : 0,
          imageUrl,
          target);
    } else {
      // 修改场景
      await Api.updateCustomScene(
          sceneId,
          yourRole,
          aiRole,
          description,
          publicPermission == PublicPermission.private ? 1 : 0,
          imageUrl,
          target);
    }
    } catch (e) {
      LogUtil.e('创建场景失败: $e');
      Navigator.of(context).pop();
      return;
    } finally {
      isSaving = false;
    }

    // 创建成功
    isSaving = false;
    // 删除本地缓存
    deleteJsonDataCache();
    // 关闭loading
    Navigator.of(context).popUntil((route) => route.isFirst);
    // 跳转到我的创建
    Navigator.of(context).push(MyCreatePage.route());
  }

  // 加载场景详情
  Future<void> loadSceneDetail(String sceneCode) async {
    final result = await Api.getSceneDetailV2(sceneCode);
    sceneId = result.data?.id ?? 0;
    target = result.data?.target ?? '';
    description = result.data?.chinese ?? '';
    imageUrl = result.data?.imageUrl ?? '';
    yourRole = result.data?.myRole ?? '';
    aiRole = result.data?.aiRole ?? '';
    // publicPermission = result.data?.publicPermission == 1 ? PublicPermission.private : PublicPermission.public;
  }

  // 一个显示[正在创建]的loading
  showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false, // 禁止点击外部关闭对话框
      builder: (BuildContext context) {
        return AlertDialog(
          insetPadding: const EdgeInsets.symmetric(horizontal: 100),
          contentPadding: EdgeInsets.only(top: 20.w, bottom: 20.w),
          content: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 50.w,
                height: 50.w,
                child: CircularProgressIndicator(
                  strokeWidth: 6.w,
                  color: const Color(0xFF84E9FF), // 设置加载指示器颜色
                ),
              ),
              SizedBox(width: 16.w), // 加一些间距
              Text(pageMode == 'create' ? "场景创建中..." : "场景修改中...",
                  style: style_1_28_400), // 加载文本
              SizedBox(width: 16.w), // 加一些间距
              // 关闭按钮
              GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Image.asset('images/dialog_close.png',
                    width: 20.w, height: 20.w),
              ),
            ],
          ),
        );
      },
    );
  }
}
