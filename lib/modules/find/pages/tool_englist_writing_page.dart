import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/permission_dialog_util.dart';
import 'package:flutter_app_kouyu/modules/find/pages/find_page.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_english_writing_recods_page.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_english_writing_submit_page.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_photo_ocr_page.dart';
import 'package:flutter_app_kouyu/modules/find/provider/tool_english_writing_page_provider.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/overlay_manager.dart';
import 'package:flutter_app_kouyu/widgets/vip_spical_widget.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';

class ToolEnglistWritingPage extends StatefulWidget {
  String compositionType = '雅思';
  String directions = '';
  String title = '';
  String wordCount = '150';

  @override
  _ToolEnglistWritingPageState createState() => _ToolEnglistWritingPageState();

  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => ToolEnglistWritingPage());
  }
}

class _ToolEnglistWritingPageState extends State<ToolEnglistWritingPage> {
  final ImagePicker picker = ImagePicker();
  int _textLength = 0;
  late TextEditingController directionsController =
      TextEditingController(text: widget.directions);

  // @override
  // void initState() {
  //   super.initState();
  //   directionsController.addListener(() {
  //     widget.directions = directionsController.text;
  //     // print('当前字数：${directionsController.text.length}');
  //   });
  // }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<ToolEnglishWritingPageProvider>(
      create: (_) => ToolEnglishWritingPageProvider()
        ..loadData()
        ..checkIsVip(),
      child: Consumer<ToolEnglishWritingPageProvider>(
        builder: (context, model, child) {
          return Stack(children: [
            Container(
              width: double.infinity,
              height: double.infinity,
              color: const Color(0xFFF3FBFD),
            ),
            Image.asset(
              "images/my_bg.png",
              width: double.infinity,
            ),
            Scaffold(
                backgroundColor: Colors.transparent,
                appBar: AppBar(
                  leadingWidth: 300,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  leading: CustomAppbar.leftWidget(context, text: "英文写作"),
                  actions: [
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context)
                            .push(ToolEnglishWritingRecodsPage.route());
                      },
                      child: Container(
                          margin: EdgeInsets.fromLTRB(24.w, 0, 24.w, 0),
                          child: Text(
                            "写作记录",
                            style: style_2_28,
                          )),
                    ),
                  ],
                ),
                body: GestureDetector(
                  onTap: () {
                    FocusScope.of(context).unfocus();
                  },
                  child: Padding(
                      padding: EdgeInsets.only(
                          left: 18.w, bottom: 198.w, right: 18.w),
                      child: ListView(
                        children: [
                          Container(
                              margin: EdgeInsets.fromLTRB(24.w, 10.w, 24.w, 0),
                              child: Text(
                                "作文等级",
                                style: style_1_32,
                              )),
                          _levelWidget(context),
                          Container(
                              margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
                              child: Text(
                                "作文字数",
                                style: style_1_32,
                              )),
                          _wordCountlWidget(context),
                          Container(
                              margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
                              child: Row(children: [
                                Text(
                                  "作文题目",
                                  style: style_1_32,
                                ),
                                Text(
                                  " *",
                                  style: TextStyle(
                                      fontSize: 36.sp,
                                      fontWeight: FontWeight.w800,
                                      color: Colors.red),
                                ),
                              ])),
                          titleInput(context, model),
                          Container(
                              margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
                              child: Text(
                                "作文要求(选填)",
                                style: style_1_32,
                              )),
                          directionsInput(context, model),
                        ],
                      )),
                ),
                floatingActionButtonLocation:
                    FloatingActionButtonLocation.centerFloat,
                floatingActionButton: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buttomButtonWidget(context, model),
                    _centerTryTimesWidget(context, model),
                  ],
                )),
          ]);
        },
      ),
    );
  }

  Widget _levelWidget(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
      child: Column(children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _compositionTypeWidget("雅思"),
            SizedBox(width: 20.w),
            _compositionTypeWidget("托福"),
            SizedBox(width: 20.w),
            _compositionTypeWidget("考研"),
            SizedBox(width: 20.w),
            _compositionTypeWidget("专业四级"),
          ],
        ),
        SizedBox(height: 20.w),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _compositionTypeWidget("专业八级"),
            SizedBox(width: 20.w),
            _compositionTypeWidget("英语四级"),
            SizedBox(width: 20.w),
            _compositionTypeWidget("英语六级"),
            SizedBox(width: 20.w),
            _compositionTypeWidget("高考"),
          ],
        ),
        SizedBox(height: 20.w),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _compositionTypeWidget("中考"),
            SizedBox(width: 20.w),
            _compositionTypeWidget("PET"),
            SizedBox(width: 20.w),
            _compositionTypeWidget("KET"),
          ],
        )
      ]),
    );
  }

  Widget titleInput(
      BuildContext context, ToolEnglishWritingPageProvider provider) {
    return Container(
      margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
      decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: const Color(0xFF41C0FF), width: 1),
          borderRadius: BorderRadius.circular(24.w)),
      child: TextField(
          controller: TextEditingController(text: widget.title),
          onChanged: (value) {
            widget.title = value;
          },
          maxLines: 1,
          decoration: InputDecoration(
              border: InputBorder.none, // 没有边框样式
              hintText: "请输入作文题目",
              hintStyle: style_3_28),
          textAlign: TextAlign.center,
          style: style_1_32_400),
    );
  }

  Widget directionsInput(
      BuildContext context, ToolEnglishWritingPageProvider provider) {
    // TextEditingController  directionsController = TextEditingController(text: widget.directions);

    return Container(
      margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
      padding: EdgeInsets.only(bottom: 10.w),
      decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: const Color(0xFF41C0FF), width: 1),
          borderRadius: BorderRadius.circular(24.w)),
      child: Column(
        children: [
          TextField(
              controller: directionsController,
              onChanged: (value) {
                widget.directions = value;

                setState(() {
                  _textLength = value.length;
                });
              },
              maxLines: 5,
              decoration: InputDecoration(
                  border: InputBorder.none, // 没有边框样式
                  hintText: "请输入写作要求, 没有可不填",
                  contentPadding: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
                  hintStyle: style_3_28),
              style: style_1_32_400),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                width: 20.w,
              ),
              Text("${_textLength}/${3000}", style: style_1_24),
              Spacer(),
              GestureDetector(
                onTap: () {
                  int num = provider.toolUserTryTimesModel?.data
                          ?.toolTryTimesEnglishWriting ??
                      0;
                  bool isVip = provider.isVip;
                  if (num <= 0 && !isVip) {
                    //体验到期
                    OverlayManager.getInstance().showOverlay(
                      true,
                      builder: (close) => VipSpicalWidget(
                        close: close,
                      ),
                    );
                    return;
                  }

                  Navigator.of(context)
                      .push(
                    MaterialPageRoute(
                      builder: (context) => ToolPhotoOcrPage(
                        toolType: ToolType.english_writing,
                        cbParam: provider.isVip ? 'vip' : '',
                      ),
                    ),
                  )
                      .then((result) {
                    if (result != null) {
                      print(result);
                      setState(() {
                        directionsController.text = result['result'];
                        widget.directions = result['result'];
                        _textLength = result['result'].length;
                      });
                    }
                  });
                },
                child: Image.asset(
                  "images/camera-line.png",
                  width: 50.w,
                  height: 50.w,
                ),
              ),
              SizedBox(
                width: 20.w,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _wordCountlWidget(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
      child: Column(children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _wordCountWidget("150"),
            SizedBox(width: 20.w),
            _wordCountWidget("300"),
            SizedBox(width: 20.w),
            _wordCountWidget("400"),
          ],
        ),
        SizedBox(height: 20.w),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _wordCountWidget("500"),
            SizedBox(width: 20.w),
            _wordCountWidget("1000"),
            SizedBox(width: 20.w),
            _wordCountWidget("不限定"),
          ],
        ),
      ]),
    );
  }

  Widget _compositionTypeWidget(String compositionType) {
    bool selected = widget.compositionType == compositionType;
    return GestureDetector(
      onTap: () {
        setState(() {
          widget.compositionType = compositionType;
        });
      },
      child: Container(
        width: 150.w,
        height: 58.w,
        decoration: selected
            ? BoxDecoration(
                borderRadius: BorderRadius.circular(24.w),
                color: Colors.white,
                border: Border.all(color: const Color(0xFF41C0FF), width: 1))
            : BoxDecoration(
                borderRadius: BorderRadius.circular(24.w),
                color: Colors.white,
              ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              compositionType,
              style: style_1_24,
            )
          ],
        ),
      ),
    );
  }

  Widget _wordCountWidget(String wordCount) {
    bool selected = widget.wordCount == wordCount;
    return GestureDetector(
      onTap: () {
        setState(() {
          widget.wordCount = wordCount;
        });
      },
      child: Container(
        width: 208.w,
        height: 58.w,
        decoration: selected
            ? BoxDecoration(
                borderRadius: BorderRadius.circular(24.w),
                color: Colors.white,
                border: Border.all(color: const Color(0xFF41C0FF), width: 1))
            : BoxDecoration(
                borderRadius: BorderRadius.circular(24.w),
                color: Colors.white,
              ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              wordCount,
              style: style_1_24,
            )
          ],
        ),
      ),
    );
  }

  Widget _buttomButtonWidget(
      BuildContext context, ToolEnglishWritingPageProvider provider) {
    return GestureDetector(
      onTap: () {
        int num =
            provider.toolUserTryTimesModel?.data?.toolTryTimesEnglishWriting ??
                0;
        bool isVip = provider.isVip;
        if (num <= 0 && !isVip) {
          //体验到期
          OverlayManager.getInstance().showOverlay(
            true,
            builder: (close) => VipSpicalWidget(
              close: close,
            ),
          );
          return;
        }

        if (widget.title.isEmpty) {
          EasyLoading.showToast("请输入作文题目");
          return;
        }
        Navigator.of(context).push(ToolEnglishWritingSubmitPage.route(
            widget.compositionType,
            widget.title,
            widget.wordCount,
            widget.directions));
      },
      child: Container(
        width: 670.w,
        height: 102.w,
        margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.w),
          color: const Color(0xFF2693FF),
        ),
        child: Center(
            child: Text(
          "生成文章",
          style: style_1_32.copyWith(color: Colors.white),
        )),
      ),
    );
  }

  Widget _centerTryTimesWidget(
      BuildContext context, ToolEnglishWritingPageProvider model) {
    // bool isVip = provider.checkIsVip();

    int num =
        model.toolUserTryTimesModel?.data?.toolTryTimesEnglishWriting ?? 0;
    if (!model.isVip)
      return Container(
          width: 670.w,
          margin: EdgeInsets.fromLTRB(24.w, 14.w, 24.w, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "剩余$num次免费使用，",
                style: style_1_24,
              ),
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pushNamed('/open_vip');
                },
                child: Text(
                  "开通学习会员畅快使用",
                  style: style_bue_24,
                ),
              ),
            ],
          ));

    return Container(
        width: 670.w,
        margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "已是会员，享受无限次免费使用",
              style: style_1_24,
            )
          ],
        ));
  }

  void _showPicker(
      BuildContext context, ToolEnglishWritingPageProvider provider) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text(
                '拍照',
                textAlign: TextAlign.center,
              ),
              onTap: () {
                _takePhoto(context, provider);
              },
            ),
            ListTile(
              title: const Text(
                '相册',
                textAlign: TextAlign.center,
              ),
              onTap: () {
                _openGallery(context, provider);
              },
            ),
            const SizedBox(height: 8),
            ListTile(
              title: const Text(
                '取消',
                textAlign: TextAlign.center,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              onTap: () {
                Navigator.pop(context); // 关闭底部模态表
              },
            ),
          ],
        );
      },
    );
  }

  _takePhoto(
      BuildContext context, ToolEnglishWritingPageProvider provider) async {
    Navigator.pop(context); // 关闭底部模态表

    // 检查相机权限
    bool hasPermission = await _checkCameraPermission(context);
    if (!hasPermission) {
      return;
    }

    XFile? pickedFile = await picker.pickImage(
        source: ImageSource.camera, maxHeight: 600, maxWidth: 600);
    if (pickedFile != null) {
      provider.tempPictureFile = pickedFile.path;
      String des = await provider.ocr(context);
      directionsController.text = des;
      setState(() {
        _textLength = directionsController.text.length;
      });
    }
  }

  /// 检查相机权限
  Future<bool> _checkCameraPermission(BuildContext context) async {
    final status = await Permission.camera.status;
    if (!status.isGranted) {
      // 无权限，则请求权限
      final agree = await PermissionDialogUtil.show(context, [
        PermissionData(
            type: PermissionType.camera,
            description: "用于拍照识别文字内容。开启后，您可拍照进行OCR识别。"),
      ]);
      if (agree != true) {
        return false;
      }
      PermissionStatus requestStatus = await Permission.camera.request();
      return requestStatus == PermissionStatus.granted;
    } else {
      return true;
    }
  }

  //打开文件夹
  Future<void> _openGallery(
      BuildContext context, ToolEnglishWritingPageProvider provider) async {
    Navigator.pop(context); // 关闭底部模态表

    // 检查相册权限
    bool hasPermission = await PermissionDialogUtil.checkAndRequestPhotoPermission(context);
    if (!hasPermission) {
      return;
    }

    XFile? pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 20,
        requestFullMetadata: false);

    if (pickedFile != null) {
      provider.tempPictureFile = pickedFile.path;

      String des = await provider.ocr(context);
      directionsController.text = des;
      setState(() {
        _textLength = directionsController.text.length;
      });
    }
  }
}
